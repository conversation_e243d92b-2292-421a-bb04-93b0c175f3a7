#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Import the landing page data
const landingPagesPath = path.join(__dirname, '../src/data/landing-pages.ts');
const publicPath = path.join(__dirname, '../public');

console.log('🔍 Validating image paths in Helevon Landing Pages...\n');

// Function to check if file exists
function fileExists(filePath) {
  try {
    return fs.existsSync(filePath);
  } catch (error) {
    return false;
  }
}

// Function to get file size
function getFileSize(filePath) {
  try {
    const stats = fs.statSync(filePath);
    return (stats.size / 1024).toFixed(2) + ' KB';
  } catch (error) {
    return 'Unknown';
  }
}

// Function to validate image path
function validateImagePath(imagePath) {
  if (!imagePath) return { valid: false, reason: 'Empty path' };
  
  // Remove leading slash for file system check
  const cleanPath = imagePath.startsWith('/') ? imagePath.slice(1) : imagePath;
  const fullPath = path.join(publicPath, cleanPath);
  
  if (!fileExists(fullPath)) {
    return { valid: false, reason: 'File not found', path: fullPath };
  }
  
  // Check file extension
  const ext = path.extname(imagePath).toLowerCase();
  const validExtensions = ['.png', '.jpg', '.jpeg', '.webp', '.avif', '.svg'];
  if (!validExtensions.includes(ext)) {
    return { valid: false, reason: 'Invalid file extension', extension: ext };
  }
  
  // Check for problematic characters
  const problematicChars = /[<>:"|?*\s()]/;
  if (problematicChars.test(imagePath)) {
    return { valid: false, reason: 'Contains problematic characters' };
  }
  
  return { 
    valid: true, 
    size: getFileSize(fullPath),
    path: fullPath
  };
}

// Read and parse the landing pages data
let landingPagesContent;
try {
  landingPagesContent = fs.readFileSync(landingPagesPath, 'utf8');
} catch (error) {
  console.error('❌ Error reading landing pages file:', error.message);
  process.exit(1);
}

// Extract image paths using regex
const imagePathRegex = /(?:imageUrl|mobileImageUrl|webImageUrl):\s*['"`]([^'"`]+)['"`]/g;
const imagePaths = [];
let match;

while ((match = imagePathRegex.exec(landingPagesContent)) !== null) {
  imagePaths.push(match[1]);
}

console.log(`📊 Found ${imagePaths.length} image references\n`);

// Validate each image path
let validCount = 0;
let invalidCount = 0;
const issues = [];

imagePaths.forEach((imagePath, index) => {
  const validation = validateImagePath(imagePath);
  
  if (validation.valid) {
    validCount++;
    console.log(`✅ ${imagePath} (${validation.size})`);
  } else {
    invalidCount++;
    console.log(`❌ ${imagePath} - ${validation.reason}`);
    issues.push({
      path: imagePath,
      reason: validation.reason,
      fullPath: validation.path
    });
  }
});

console.log('\n📈 Summary:');
console.log(`✅ Valid images: ${validCount}`);
console.log(`❌ Invalid images: ${invalidCount}`);

if (issues.length > 0) {
  console.log('\n🚨 Issues found:');
  issues.forEach((issue, index) => {
    console.log(`${index + 1}. ${issue.path}`);
    console.log(`   Reason: ${issue.reason}`);
    if (issue.fullPath) {
      console.log(`   Expected at: ${issue.fullPath}`);
    }
    console.log('');
  });
  
  console.log('💡 Recommendations:');
  console.log('1. Ensure all image files exist in the public directory');
  console.log('2. Use clean file names without spaces or special characters');
  console.log('3. Use supported image formats: .png, .jpg, .jpeg, .webp, .avif, .svg');
  console.log('4. Check file paths are correctly referenced in the data files');
  
  process.exit(1);
} else {
  console.log('\n🎉 All images are valid and accessible!');
  console.log('✨ Your image optimization setup is working correctly.');
}
