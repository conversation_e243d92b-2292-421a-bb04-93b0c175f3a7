import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  images: {
    // Enable image optimization with modern formats
    formats: ['image/webp', 'image/avif'],
    // Add device sizes for responsive images
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    // Add image sizes for different breakpoints
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
    // Optimize for local images
    unoptimized: false,
    // Add domains if using external images (currently not needed)
    domains: [],
    // Add remote patterns if needed (currently not needed)
    remotePatterns: [],
  },
  // Enable experimental features for better performance
  experimental: {
    optimizePackageImports: ['lucide-react', 'framer-motion'],
  },
  // Compress responses
  compress: true,
};

export default nextConfig;
