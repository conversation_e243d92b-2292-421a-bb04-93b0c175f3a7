'use client';

import { useState, useCallback } from 'react';
import Image, { ImageProps } from 'next/image';
import { getImageFallback, validateImagePath } from '@/lib/utils';

interface EnhancedImageProps extends Omit<ImageProps, 'onError'> {
  fallbackText?: string;
  showFallback?: boolean;
  onLoadError?: (error: Error) => void;
}

export function EnhancedImage({
  src,
  alt,
  fallbackText,
  showFallback = true,
  onLoadError,
  className = '',
  ...props
}: EnhancedImageProps) {
  const [hasError, setHasError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  const handleError = useCallback((error: any) => {
    console.warn(`Image failed to load: ${src}`, error);
    setHasError(true);
    setIsLoading(false);
    
    if (onLoadError) {
      onLoadError(new Error(`Failed to load image: ${src}`));
    }
  }, [src, onLoadError]);

  const handleLoad = useCallback(() => {
    setIsLoading(false);
    setHasError(false);
  }, []);

  // Validate image path
  if (!src || !validateImagePath(src.toString())) {
    console.warn(`Invalid image path: ${src}`);
    if (!showFallback) return null;
    
    return (
      <div 
        className={`flex items-center justify-center bg-gray-100 ${className}`}
        style={props.fill ? {} : { width: props.width, height: props.height }}
      >
        <div className="text-gray-400 text-center p-4">
          <div className="text-2xl font-bold mb-2">
            {fallbackText?.charAt(0) || alt.charAt(0) || '?'}
          </div>
          <div className="text-xs">Image not available</div>
        </div>
      </div>
    );
  }

  // Show fallback if error occurred
  if (hasError && showFallback) {
    return (
      <div 
        className={`flex items-center justify-center bg-gradient-to-br from-gray-100 to-gray-200 ${className}`}
        style={props.fill ? {} : { width: props.width, height: props.height }}
      >
        <div className="text-gray-500 text-center p-4">
          <div className="text-3xl font-bold mb-2 text-gray-400">
            {fallbackText?.charAt(0) || alt.charAt(0) || '?'}
          </div>
          <div className="text-xs">Failed to load image</div>
        </div>
      </div>
    );
  }

  // Don't render anything if error and no fallback
  if (hasError && !showFallback) {
    return null;
  }

  return (
    <div className="relative">
      {/* Loading placeholder */}
      {isLoading && (
        <div 
          className={`absolute inset-0 flex items-center justify-center bg-gray-100 animate-pulse ${className}`}
          style={props.fill ? {} : { width: props.width, height: props.height }}
        >
          <div className="text-gray-400">
            <div className="w-8 h-8 border-2 border-gray-300 border-t-blue-500 rounded-full animate-spin"></div>
          </div>
        </div>
      )}
      
      {/* Actual image */}
      <Image
        {...props}
        src={src}
        alt={alt}
        className={`${className} ${isLoading ? 'opacity-0' : 'opacity-100'} transition-opacity duration-300`}
        onLoad={handleLoad}
        onError={handleError}
        // Add priority for above-the-fold images
        priority={props.priority || false}
        // Add placeholder for better UX
        placeholder={props.placeholder || 'blur'}
        blurDataURL={props.blurDataURL || getImageFallback(alt)}
      />
    </div>
  );
}

// Specialized component for project images
interface ProjectImageProps extends Omit<EnhancedImageProps, 'fallbackText'> {
  projectName: string;
  projectType?: 'mobile' | 'web' | 'both';
}

export function ProjectImage({ 
  projectName, 
  projectType = 'web',
  className = '',
  ...props 
}: ProjectImageProps) {
  const isVertical = projectType === 'mobile';
  
  return (
    <EnhancedImage
      {...props}
      fallbackText={projectName}
      className={`${className} ${isVertical ? 'object-contain' : 'object-cover'}`}
      alt={`${projectName} ${projectType} application`}
    />
  );
}
