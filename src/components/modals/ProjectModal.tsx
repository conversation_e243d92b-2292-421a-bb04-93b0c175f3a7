'use client';

import { motion } from 'framer-motion';
import { Modal } from '@/components/ui/modal';
import { MockProject } from '@/types';
import { ExternalLink, Users, DollarSign, TrendingUp, Calendar, Code, Target, Award } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { EnhancedImage, ProjectImage } from '@/components/ui/enhanced-image';

interface ProjectModalProps {
  isOpen: boolean;
  onClose: () => void;
  project: MockProject | null;
  onStartProject?: () => void;
}

export function ProjectModal({ isOpen, onClose, project, onStartProject }: ProjectModalProps) {
  if (!project) return null;

  const handleStartProject = () => {
    onClose(); // Close the modal first
    if (onStartProject) {
      onStartProject(); // Trigger contact form
    } else {
      // Fallback: scroll to contact form
      const contactForm = document.getElementById('contact-form');
      if (contactForm) {
        contactForm.scrollIntoView({ behavior: 'smooth' });
      }
    }
  };

  const projectDetails = {
    timeline: '8-12 weeks',
    team: '4 developers',
    technologies: project.tags,
    challenges: [
      'Scalable architecture design',
      'Real-time data processing',
      'User experience optimization',
      'Performance optimization'
    ],
    results: [
      'Successful product launch',
      'Positive user feedback',
      'Rapid user acquisition',
      'Revenue generation'
    ]
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="xl">
      <div className="relative">
        {/* Scroll Indicator */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1, duration: 0.5 }}
          className="absolute top-4 right-4 z-10 flex flex-col items-center text-white/70"
        >
          <motion.div
            animate={{ y: [0, 6, 0] }}
            transition={{ duration: 1.5, repeat: Infinity, ease: "easeInOut" }}
            className="text-xs font-medium mb-1"
          >
            Scroll
          </motion.div>
          <motion.div
            animate={{ y: [0, 6, 0] }}
            transition={{ duration: 1.5, repeat: Infinity, ease: "easeInOut", delay: 0.2 }}
            className="w-0.5 h-4 bg-gradient-to-b from-white/70 to-transparent rounded-full"
          />
        </motion.div>

        {/* Hero Section */}
        <div className="relative h-64 bg-gradient-to-br from-blue-600 to-purple-600 overflow-hidden">
          {project.imageUrl ? (
            <EnhancedImage
              src={project.imageUrl}
              alt={project.name}
              fill
              className="object-cover"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              fallbackText={project.name}
              priority={true}
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center">
              <div className="text-8xl font-bold text-white/20">
                {project.name.charAt(0)}
              </div>
            </div>
          )}
          <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />

          {/* Project Info Overlay */}
          <div className="absolute bottom-6 left-6 text-white">
            <div className="flex items-center space-x-2 mb-2">
              <span className="px-3 py-1 bg-white/20 backdrop-blur-sm rounded-full text-sm font-medium">
                {project.category}
              </span>
              {project.type && (
                <span className="px-3 py-1 bg-blue-500/80 backdrop-blur-sm rounded-full text-sm font-medium">
                  {project.type === 'both' ? 'Web + Mobile' : project.type}
                </span>
              )}
            </div>
            <h1 className="text-3xl font-bold mb-2">{project.name}</h1>
            <p className="text-white/90 max-w-md">{project.description}</p>
          </div>
        </div>

        {/* Project Images Gallery */}
        {(project.mobileImageUrl || project.webImageUrl) && (
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Project Screenshots</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {project.mobileImageUrl && (
                <div className="space-y-2">
                  <h4 className="text-sm font-medium text-gray-700">Mobile App</h4>
                  <div className="relative bg-gray-100 rounded-lg overflow-hidden flex justify-center items-center p-4" style={{ minHeight: '300px' }}>
                    <ProjectImage
                      src={project.mobileImageUrl}
                      alt={`${project.name} Mobile`}
                      projectName={project.name}
                      projectType="mobile"
                      width={200}
                      height={400}
                      className="object-contain max-h-full w-auto rounded-lg shadow-lg"
                    />
                  </div>
                </div>
              )}
              {project.webImageUrl && (
                <div className="space-y-2">
                  <h4 className="text-sm font-medium text-gray-700">Web Application</h4>
                  <div className="relative bg-gray-100 rounded-lg overflow-hidden" style={{ minHeight: '300px' }}>
                    <ProjectImage
                      src={project.webImageUrl}
                      alt={`${project.name} Web`}
                      projectName={project.name}
                      projectType="web"
                      fill
                      className="object-contain rounded-lg"
                    />
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Equity Story */}
        {project.story && (
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-3">Partnership Story</h3>
            <div className="bg-blue-50 border-l-4 border-blue-500 p-4 rounded-r-lg">
              <p className="text-blue-800 italic">&ldquo;{project.story}&rdquo;</p>
            </div>
          </div>
        )}

        {/* Content */}
        <div className="p-6">
          {/* Stats */}
          {project.stats && (
            <div className="grid grid-cols-3 gap-4 mb-8">
              {project.stats.equity && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.1 }}
                  className="text-center p-4 bg-blue-50 rounded-xl"
                >
                  <Target className="w-8 h-8 text-blue-600 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-blue-600">{project.stats.equity}</div>
                  <div className="text-sm text-gray-600">Equity Partnership</div>
                </motion.div>
              )}
              {project.stats.stage && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2 }}
                  className="text-center p-4 bg-green-50 rounded-xl"
                >
                  <Award className="w-8 h-8 text-green-600 mx-auto mb-2" />
                  <div className="text-lg font-bold text-green-600">{project.stats.stage}</div>
                  <div className="text-sm text-gray-600">Current Stage</div>
                </motion.div>
              )}
              {project.stats.founder && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3 }}
                  className="text-center p-4 bg-purple-50 rounded-xl"
                >
                  <Users className="w-8 h-8 text-purple-600 mx-auto mb-2" />
                  <div className="text-sm font-bold text-purple-600">{project.stats.founder}</div>
                  <div className="text-sm text-gray-600">Founder Profile</div>
                </motion.div>
              )}

              {/* Legacy stats for backward compatibility */}
              {project.stats.users && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.1 }}
                  className="text-center p-4 bg-blue-50 rounded-xl"
                >
                  <Users className="w-8 h-8 text-blue-600 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-blue-600">{project.stats.users}</div>
                  <div className="text-sm text-gray-600">Active Users</div>
                </motion.div>
              )}
              {project.stats.revenue && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2 }}
                  className="text-center p-4 bg-green-50 rounded-xl"
                >
                  <DollarSign className="w-8 h-8 text-green-600 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-green-600">{project.stats.revenue}</div>
                  <div className="text-sm text-gray-600">Monthly Revenue</div>
                </motion.div>
              )}
              {project.stats.growth && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3 }}
                  className="text-center p-4 bg-purple-50 rounded-xl"
                >
                  <TrendingUp className="w-8 h-8 text-purple-600 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-purple-600">{project.stats.growth}</div>
                  <div className="text-sm text-gray-600">Growth Rate</div>
                </motion.div>
              )}
            </div>
          )}

          {/* Project Details */}
          <div className="grid md:grid-cols-2 gap-8 mb-8">
            {/* About */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.4 }}
            >
              <h3 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                <Target className="w-5 h-5 mr-2 text-blue-600" />
                About This Project
              </h3>
              <p className="text-gray-600 mb-4 leading-relaxed">
                {project.description} This project showcases our ability to deliver high-quality, 
                scalable solutions that drive real business results for our equity partners.
              </p>
              
              <div className="space-y-3">
                <div className="flex items-center text-sm text-gray-600">
                  <Calendar className="w-4 h-4 mr-2" />
                  <span>Timeline: {projectDetails.timeline}</span>
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <Users className="w-4 h-4 mr-2" />
                  <span>Team Size: {projectDetails.team}</span>
                </div>
              </div>
            </motion.div>

            {/* Technologies */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.5 }}
            >
              <h3 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                <Code className="w-5 h-5 mr-2 text-blue-600" />
                Technologies Used
              </h3>
              <div className="flex flex-wrap gap-2 mb-6">
                {project.tags.map((tag, index) => (
                  <span
                    key={index}
                    className="px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-sm font-medium"
                  >
                    {tag}
                  </span>
                ))}
              </div>

              <h4 className="font-semibold text-gray-900 mb-3">Key Challenges Solved</h4>
              <ul className="space-y-2">
                {projectDetails.challenges.map((challenge, index) => (
                  <li key={index} className="flex items-start text-sm text-gray-600">
                    <div className="w-1.5 h-1.5 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0" />
                    {challenge}
                  </li>
                ))}
              </ul>
            </motion.div>
          </div>

          {/* Results */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
            className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-6 mb-8"
          >
            <h3 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
              <Award className="w-5 h-5 mr-2 text-blue-600" />
              Project Results
            </h3>
            <div className="grid md:grid-cols-2 gap-4">
              {projectDetails.results.map((result, index) => (
                <div key={index} className="flex items-center text-gray-700">
                  <div className="w-2 h-2 bg-green-500 rounded-full mr-3" />
                  {result}
                </div>
              ))}
            </div>
          </motion.div>

          {/* CTA */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.7 }}
            className="text-center"
          >
            <h3 className="text-xl font-semibold text-gray-900 mb-4">
              Ready to build something amazing together?
            </h3>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                variant="gradient"
                size="lg"
                className="group"
                onClick={handleStartProject}
              >
                Start Your Project
                <ExternalLink className="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform" />
              </Button>
              <Button
                variant="outline"
                size="lg"
                onClick={onClose}
              >
                Close Modal
              </Button>
            </div>
          </motion.div>
        </div>
      </div>
    </Modal>
  );
}
