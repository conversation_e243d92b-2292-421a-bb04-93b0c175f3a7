import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

export function formatPhoneNumber(phone: string): string {
  const cleaned = phone.replace(/\D/g, '');
  const match = cleaned.match(/^(\d{3})(\d{3})(\d{4})$/);
  if (match) {
    return `(${match[1]}) ${match[2]}-${match[3]}`;
  }
  return phone;
}

export function generateWhatsAppLink(phone: string, message: string): string {
  const cleanPhone = phone.replace(/\D/g, '');
  const encodedMessage = encodeURIComponent(message);
  return `https://wa.me/${cleanPhone}?text=${encodedMessage}`;
}

export function scrollToElement(elementId: string, offset: number = 0): void {
  const element = document.getElementById(elementId);
  if (element) {
    const elementPosition = element.getBoundingClientRect().top;
    const offsetPosition = elementPosition + window.pageYOffset - offset;
    
    window.scrollTo({
      top: offsetPosition,
      behavior: 'smooth'
    });
  }
}

export function debounce<T extends (...args: unknown[]) => unknown>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

// Image utilities for better error handling and optimization
export function getImagePath(path: string): string {
  // Ensure the path starts with /
  if (!path.startsWith('/')) {
    return `/${path}`;
  }
  return path;
}

export function validateImagePath(path: string): boolean {
  // Check if path is valid and doesn't contain problematic characters
  const invalidChars = /[<>:"|?*\s()]/;
  return !invalidChars.test(path) && path.length > 0;
}

export function getImageFallback(projectName: string): string {
  // Generate a fallback image path or data URL
  return `data:image/svg+xml;base64,${btoa(`
    <svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
      <rect width="100%" height="100%" fill="#f3f4f6"/>
      <text x="50%" y="50%" font-family="Arial, sans-serif" font-size="24"
            fill="#6b7280" text-anchor="middle" dy=".3em">
        ${projectName.charAt(0)}
      </text>
    </svg>
  `)}`;
}

export function optimizeImageUrl(url: string, width?: number, quality?: number): string {
  // For Next.js Image component optimization
  if (!url) return '';

  const params = new URLSearchParams();
  if (width) params.set('w', width.toString());
  if (quality) params.set('q', quality.toString());

  const queryString = params.toString();
  return queryString ? `${url}?${queryString}` : url;
}
